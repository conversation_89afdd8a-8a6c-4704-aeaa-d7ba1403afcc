package com.tqhit.battery.one.features.emoji.presentation.overlay.permission

import android.content.Context
import android.util.Log
import com.tqhit.battery.one.features.emoji.presentation.overlay.EmojiAccessibilityServiceManager
import com.tqhit.battery.one.utils.BatteryLogger
import kotlinx.coroutines.CoroutineScope
import kotlinx.coroutines.SupervisorJob
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Unified manager specifically for emoji overlay toggle logic used by both 
 * EmojiBatteryFragment and EmojiCustomizeActivity.
 * 
 * This manager provides a single point of control for emoji toggle functionality
 * and ensures consistent permission handling and user experience across both screens.
 * 
 * Key Features:
 * - Single handleEmojiToggleChange() method for both screens
 * - Uses EmojiOverlayPermissionManager for consistent permission flow
 * - Implements user's required flow: check accessibility permission → enable toggle or show dialog
 * - <PERSON>les dialog outcomes: continue → auto-enable toggle, dismiss → keep disabled
 * - Never emits "permission denied" status as per user requirements
 * - Coordinates with EmojiAccessibilityPermissionTrackerService for permission changes
 * - Provides callbacks for UI updates: onToggleEnabled, onToggleDisabled, onPermissionDialogShown
 * - Comprehensive logging with emoji-specific tags for debugging
 * - Works specifically with emoji overlay functionality and EmojiAccessibilityServiceManager
 * - Exposes StateFlow<Boolean> for toggle state that both screens can observe
 */
@Singleton
class EmojiUnifiedToggleManager @Inject constructor(
    private val emojiAccessibilityServiceManager: EmojiAccessibilityServiceManager,
    private val permissionTrackerService: EmojiAccessibilityPermissionTrackerService
) {

    companion object {
        private const val TAG = "EmojiUnifiedToggleManager"
        private const val EMOJI_TOGGLE_TAG = "EmojiToggle_Manager"
        private const val EMOJI_TOGGLE_FLOW_TAG = "EMOJI_TOGGLE_FLOW"
        private const val EMOJI_TOGGLE_PERMISSION_TAG = "EmojiToggle_Permission"
        private const val EMOJI_PERMISSION_DIALOG_TAG = "EMOJI_PERMISSION_DIALOG"
        private const val EMOJI_STATE_SYNC_TAG = "EMOJI_STATE_SYNC"
    }

    // Coroutine scope for the manager
    private val managerScope = CoroutineScope(SupervisorJob())

    // Toggle state flow - represents the actual emoji toggle state
    private val _toggleState = MutableStateFlow(false)
    val toggleState: StateFlow<Boolean> = _toggleState.asStateFlow()

    // UI callback interfaces
    interface ToggleCallbacks {
        fun onToggleEnabled() {}
        fun onToggleDisabled() {}
        fun onPermissionDialogShown() {}
    }

    /**
     * Handles emoji toggle state changes for both gallery and customization screens.
     * This is the single entry point for all emoji toggle operations.
     * 
     * Implements the user's required flow:
     * 1. Check accessibility permission
     * 2. If true: enable toggle and show overlay
     * 3. If false: show accessibility dialog
     * 4. Handle dialog outcomes: continue → auto-enable, dismiss → keep disabled
     * 
     * @param isEnabled The desired toggle state
     * @param context The context for permission checking and dialog display
     * @param callbacks Optional callbacks for UI updates
     */
    fun handleEmojiToggleChange(
        isEnabled: Boolean,
        context: Context,
        callbacks: ToggleCallbacks? = null
    ) {
        BatteryLogger.d(TAG, "Handling emoji toggle change - isEnabled: $isEnabled")
        BatteryLogger.d(EMOJI_TOGGLE_FLOW_TAG, "EMOJI_TOGGLE_CHANGE_REQUESTED: $isEnabled")
        
        try {
            if (isEnabled) {
                handleEnableToggle(context, callbacks)
            } else {
                handleDisableToggle(context, callbacks)
            }
        } catch (e: Exception) {
            BatteryLogger.e(TAG, "Error handling emoji toggle change: ${e.message}")
            Log.e(TAG, "Error in handleEmojiToggleChange", e)
            
            // Ensure toggle stays disabled on error
            updateToggleState(false)
            callbacks?.onToggleDisabled()
        }
    }

    /**
     * Handles enabling the emoji toggle.
     * Checks permissions and either enables immediately or shows permission dialog.
     */
    private fun handleEnableToggle(context: Context, callbacks: ToggleCallbacks?) {
        BatteryLogger.d(TAG, "Handling enable emoji toggle")
        BatteryLogger.d(EMOJI_TOGGLE_PERMISSION_TAG, "EMOJI_TOGGLE_ENABLE_REQUESTED")

        // Check if all required permissions are granted
        if (EmojiOverlayPermissionManager.hasAllRequiredPermissions(context)) {
            BatteryLogger.d(EMOJI_TOGGLE_PERMISSION_TAG, "All permissions granted, enabling emoji toggle")
            enableEmojiToggle(context, callbacks)
        } else {
            BatteryLogger.d(EMOJI_TOGGLE_PERMISSION_TAG, "Permissions missing, immediately setting toggle to false and showing permission flow")

            // CRITICAL FIX: Immediately set toggle state to false when permissions are missing
            // This ensures the StateFlow emits false, which reverts the UI toggle if dialog is dismissed
            updateToggleState(false)

            // Show permission dialog
            showPermissionDialog(context, callbacks)
        }
    }

    /**
     * Handles disabling the emoji toggle.
     * Immediately disables the toggle and stops the emoji overlay.
     */
    private fun handleDisableToggle(context: Context, callbacks: ToggleCallbacks?) {
        BatteryLogger.d(TAG, "Handling disable emoji toggle")
        BatteryLogger.d(EMOJI_TOGGLE_FLOW_TAG, "EMOJI_TOGGLE_DISABLE_REQUESTED")
        
        try {
            // Update toggle state first
            updateToggleState(false)
            
            // Stop emoji overlay service
            emojiAccessibilityServiceManager.stopEmojiAccessibilityService()
            
            // Notify UI callback
            callbacks?.onToggleDisabled()
            
            BatteryLogger.d(EMOJI_TOGGLE_FLOW_TAG, "EMOJI_TOGGLE_DISABLED_SUCCESSFULLY")
            Log.d(TAG, "Emoji toggle disabled successfully")
            
        } catch (e: Exception) {
            BatteryLogger.e(TAG, "Error disabling emoji toggle: ${e.message}")
            Log.e(TAG, "Error in handleDisableToggle", e)
        }
    }

    /**
     * Enables the emoji toggle and starts the overlay service.
     * Called when permissions are already granted.
     */
    private fun enableEmojiToggle(context: Context, callbacks: ToggleCallbacks?) {
        try {
            BatteryLogger.d(TAG, "Enabling emoji toggle with permissions granted")
            
            // Update toggle state
            updateToggleState(true)
            
            // Start emoji overlay service
            emojiAccessibilityServiceManager.startEmojiAccessibilityService()
            
            // Notify UI callback
            callbacks?.onToggleEnabled()
            
            BatteryLogger.d(EMOJI_TOGGLE_FLOW_TAG, "EMOJI_TOGGLE_ENABLED_SUCCESSFULLY")
            Log.d(TAG, "Emoji toggle enabled successfully")
            
        } catch (e: Exception) {
            BatteryLogger.e(TAG, "Error enabling emoji toggle: ${e.message}")
            Log.e(TAG, "Error in enableEmojiToggle", e)
            
            // Revert toggle state on error
            updateToggleState(false)
            callbacks?.onToggleDisabled()
        }
    }

    /**
     * Shows the permission dialog using the enhanced accessibility permission flow.
     * Implements the user's specified dialog outcome handling.
     */
    private fun showPermissionDialog(context: Context, callbacks: ToggleCallbacks?) {
        BatteryLogger.d(TAG, "Showing emoji permission dialog")
        BatteryLogger.d(EMOJI_TOGGLE_PERMISSION_TAG, "EMOJI_PERMISSION_DIALOG_REQUESTED")

        // Enhanced logging for dialog flow verification
        Log.i(EMOJI_PERMISSION_DIALOG_TAG, "🔐 PERMISSION_DIALOG_STARTING: About to show accessibility permission dialog")
        Log.d(EMOJI_TOGGLE_FLOW_TAG, "📋 DIALOG_FLOW: Current toggle state before dialog: ${getCurrentToggleState()}")

        try {
            // Notify that dialog is being shown
            callbacks?.onPermissionDialogShown()
            Log.d(EMOJI_PERMISSION_DIALOG_TAG, "🔐 DIALOG_CALLBACK: onPermissionDialogShown() called")

            // Use enhanced permission flow from EmojiOverlayPermissionManager
            EmojiOverlayPermissionManager.checkPermissionsAndProceed(
                context = context,
                onAllPermissionsGranted = {
                    BatteryLogger.d(EMOJI_TOGGLE_PERMISSION_TAG, "User continued and granted permissions")
                    Log.i(EMOJI_PERMISSION_DIALOG_TAG, "✅ DIALOG_RESULT: User GRANTED permissions")
                    Log.d(EMOJI_TOGGLE_FLOW_TAG, "📋 PERMISSION_GRANTED: Will auto-enable toggle")
                    // Auto-enable toggle when permission granted (user's requirement)
                    handlePermissionGranted(context, callbacks)
                },
                onPermissionsDenied = {
                    BatteryLogger.d(EMOJI_TOGGLE_PERMISSION_TAG, "User dismissed permission dialog")
                    Log.i(EMOJI_PERMISSION_DIALOG_TAG, "❌ DIALOG_RESULT: User DISMISSED/DENIED permissions")
                    Log.d(EMOJI_TOGGLE_FLOW_TAG, "📋 PERMISSION_DENIED: Will keep toggle disabled")
                    // Keep toggle disabled when user dismisses (user's requirement)
                    handlePermissionDenied(callbacks)
                },
                showExplanation = true
            )

            BatteryLogger.d(EMOJI_TOGGLE_PERMISSION_TAG, "EMOJI_PERMISSION_DIALOG_SHOWN")
            Log.i(EMOJI_PERMISSION_DIALOG_TAG, "🔐 DIALOG_SETUP_COMPLETE: Permission dialog callbacks configured")

        } catch (e: Exception) {
            BatteryLogger.e(TAG, "Error showing permission dialog: ${e.message}")
            Log.e(TAG, "Error in showPermissionDialog", e)
            Log.e(EMOJI_PERMISSION_DIALOG_TAG, "💥 DIALOG_ERROR: Failed to show permission dialog")

            // Keep toggle disabled on error
            updateToggleState(false)
            callbacks?.onToggleDisabled()
        }
    }

    /**
     * Handles permission granted outcome.
     * Auto-enables toggle as per user requirements.
     */
    private fun handlePermissionGranted(context: Context, callbacks: ToggleCallbacks?) {
        BatteryLogger.d(TAG, "Permission granted, auto-enabling emoji toggle")
        BatteryLogger.d(EMOJI_TOGGLE_PERMISSION_TAG, "EMOJI_PERMISSION_GRANTED_AUTO_ENABLE")
        
        // Auto-enable the toggle (user's requirement)
        enableEmojiToggle(context, callbacks)
    }

    /**
     * Handles permission denied outcome.
     * Keeps toggle disabled as per user requirements.
     */
    private fun handlePermissionDenied(callbacks: ToggleCallbacks?) {
        BatteryLogger.d(TAG, "Permission denied, keeping emoji toggle disabled")
        BatteryLogger.d(EMOJI_TOGGLE_PERMISSION_TAG, "EMOJI_PERMISSION_DENIED_KEEP_DISABLED")

        // Enhanced logging for dialog dismissal verification
        Log.i(EMOJI_PERMISSION_DIALOG_TAG, "❌ PERMISSION_DENIED_HANDLER: Processing dialog dismissal")
        Log.d(EMOJI_TOGGLE_FLOW_TAG, "📋 DENIAL_FLOW: About to set toggle state to FALSE")
        Log.i(EMOJI_STATE_SYNC_TAG, "🔄 CRITICAL_MOMENT: This will trigger StateFlow emission to revert UI toggle")

        // Keep toggle disabled (user's requirement)
        updateToggleState(false)
        callbacks?.onToggleDisabled()

        Log.d(EMOJI_PERMISSION_DIALOG_TAG, "❌ DENIAL_COMPLETE: Toggle disabled, UI should now show OFF")

        // Note: Never emit "permission denied" status as per user requirements
        // Only track enabled/disabled states
    }

    /**
     * Updates the toggle state and logs the change.
     * This method emits the new state through StateFlow, which triggers UI updates.
     */
    private fun updateToggleState(isEnabled: Boolean) {
        val previousState = _toggleState.value
        if (previousState != isEnabled) {
            _toggleState.value = isEnabled

            // Comprehensive logging for verification
            BatteryLogger.d(EMOJI_TOGGLE_TAG, "EMOJI_TOGGLE_STATE_CHANGED: $previousState -> $isEnabled")
            Log.d(TAG, "Toggle state changed: $previousState -> $isEnabled")
            Log.d(EMOJI_STATE_SYNC_TAG, "🔄 StateFlow EMITTING: $isEnabled to UI observers")
            Log.d(EMOJI_TOGGLE_FLOW_TAG, "📡 MANAGER_STATE_UPDATE: previous=$previousState, new=$isEnabled")

            // Log the exact moment StateFlow emits for debugging
            Log.i(EMOJI_STATE_SYNC_TAG, "⚡ CRITICAL: StateFlow emission will trigger UI updates in Fragment and Activity")

        } else {
            Log.d(TAG, "Toggle state unchanged: $isEnabled")
            Log.d(EMOJI_STATE_SYNC_TAG, "🔄 StateFlow NOT emitting (state unchanged): $isEnabled")
        }
    }

    /**
     * Gets the current toggle state synchronously.
     * Useful for immediate state checks without observing the flow.
     * 
     * @return true if emoji toggle is enabled, false otherwise
     */
    fun getCurrentToggleState(): Boolean {
        return _toggleState.value
    }

    /**
     * Initializes the toggle manager and sets up permission tracking.
     * Should be called when the manager is first used.
     */
    fun initialize(context: Context) {
        BatteryLogger.d(TAG, "Initializing emoji unified toggle manager")
        BatteryLogger.d(EMOJI_TOGGLE_TAG, "EMOJI_TOGGLE_MANAGER_INITIALIZE")
        
        try {
            // Start permission tracking service
            EmojiAccessibilityPermissionTrackerService.startTracking(context)
            
            // Set initial toggle state based on current permissions
            val initialState = EmojiOverlayPermissionManager.hasAllRequiredPermissions(context)
            updateToggleState(initialState)
            
            // Set up permission tracking for automatic state updates
            setupPermissionTracking()
            
            BatteryLogger.d(EMOJI_TOGGLE_TAG, "EMOJI_TOGGLE_MANAGER_INITIALIZED")
            Log.d(TAG, "Emoji unified toggle manager initialized successfully")
            
        } catch (e: Exception) {
            BatteryLogger.e(TAG, "Error initializing emoji toggle manager: ${e.message}")
            Log.e(TAG, "Error in initialize", e)
        }
    }

    /**
     * Sets up automatic permission tracking to update toggle state.
     * Observes permission changes and updates toggle accordingly.
     */
    private fun setupPermissionTracking() {
        managerScope.launch {
            try {
                // Observe permission status changes
                permissionTrackerService.permissionStatus.collect { isPermissionGranted ->
                    BatteryLogger.d(TAG, "Permission status changed to: $isPermissionGranted")
                    
                    // Update toggle state based on permission status
                    // Only auto-enable if permission was just granted and toggle should be enabled
                    if (isPermissionGranted && getCurrentToggleState()) {
                        BatteryLogger.d(EMOJI_TOGGLE_PERMISSION_TAG, "Permission granted, maintaining enabled state")
                        // Toggle is already enabled, ensure overlay service is running
                        emojiAccessibilityServiceManager.startEmojiAccessibilityService()
                    } else if (!isPermissionGranted) {
                        BatteryLogger.d(EMOJI_TOGGLE_PERMISSION_TAG, "Permission lost, disabling toggle")
                        // Permission lost, disable toggle
                        updateToggleState(false)
                    }
                }
            } catch (e: Exception) {
                BatteryLogger.e(TAG, "Error in permission tracking: ${e.message}")
                Log.e(TAG, "Error in setupPermissionTracking", e)
            }
        }
    }

    /**
     * Cleans up the toggle manager resources.
     * Should be called when the manager is no longer needed.
     */
    fun cleanup(context: Context) {
        BatteryLogger.d(TAG, "Cleaning up emoji unified toggle manager")
        BatteryLogger.d(EMOJI_TOGGLE_TAG, "EMOJI_TOGGLE_MANAGER_CLEANUP")
        
        try {
            // Stop permission tracking service
            EmojiAccessibilityPermissionTrackerService.stopTracking(context)
            
            BatteryLogger.d(EMOJI_TOGGLE_TAG, "EMOJI_TOGGLE_MANAGER_CLEANED_UP")
            Log.d(TAG, "Emoji unified toggle manager cleaned up")
            
        } catch (e: Exception) {
            BatteryLogger.e(TAG, "Error cleaning up emoji toggle manager: ${e.message}")
            Log.e(TAG, "Error in cleanup", e)
        }
    }
}
