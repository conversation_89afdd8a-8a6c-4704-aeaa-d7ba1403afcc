package com.tqhit.battery.one.features.emoji.presentation.customize

import android.content.Context
import android.content.Intent
import android.os.Build
import android.os.Bundle
import android.util.Log
import android.view.MenuItem
import android.widget.SeekBar
import android.widget.TextView
import androidx.activity.viewModels
import androidx.activity.OnBackPressedCallback
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.repeatOnLifecycle
import androidx.recyclerview.widget.LinearLayoutManager
import com.bumptech.glide.Glide
import com.bumptech.glide.load.engine.GlideException
import com.bumptech.glide.request.RequestListener
import com.bumptech.glide.request.target.Target
import com.tqhit.battery.one.R
import com.tqhit.battery.one.base.LocaleAwareActivity
import com.tqhit.battery.one.databinding.ActivityEmojiCustomizeBinding
import com.tqhit.battery.one.features.emoji.domain.model.BatteryStyle
import com.tqhit.battery.one.features.emoji.domain.model.CustomizationConfig
import com.tqhit.battery.one.features.emoji.presentation.gallery.adapter.BatteryStyleAdapter
import com.tqhit.battery.one.features.emoji.presentation.customize.adapter.BatteryComponentAdapter
import com.tqhit.battery.one.features.emoji.presentation.customize.adapter.EmojiComponentAdapter
import com.tqhit.battery.one.features.emoji.presentation.overlay.permission.EmojiOverlayPermissionManager
import com.tqhit.battery.one.manager.theme.ThemeManager
import com.tqhit.battery.one.repository.AppRepository
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.launch
import javax.inject.Inject
import android.graphics.drawable.Drawable
import com.bumptech.glide.load.DataSource

/**
 * Standalone Activity for customizing emoji battery styles.
 * Provides live preview and customization options for selected battery styles.
 * 
 * This Activity follows the established patterns in the app:
 * - Extends LocaleAwareActivity for proper locale support
 * - Uses ViewBinding for view access
 * - Implements MVI pattern with ViewModel
 * - Uses Hilt for dependency injection
 * - Follows Material 3 design guidelines
 * - Includes proper ActionBar/Toolbar with back navigation
 * - Adapts to current app theme (dark/light mode)
 */
@AndroidEntryPoint
class EmojiCustomizeActivity : LocaleAwareActivity<ActivityEmojiCustomizeBinding>() {
    
    companion object {
        private const val TAG = "EmojiCustomizeActivity"
        private const val EXTRA_BATTERY_STYLE = "battery_style"
        private const val EXTRA_CATEGORY_ID = "category_id"
        
        /**
         * Creates an intent to launch EmojiCustomizeActivity with a battery style.
         */
        fun createIntent(context: Context, batteryStyle: BatteryStyle): Intent {
            return Intent(context, EmojiCustomizeActivity::class.java).apply {
                putExtra(EXTRA_BATTERY_STYLE, batteryStyle)
            }
        }
        
        /**
         * Creates an intent to launch EmojiCustomizeActivity with a battery style and category ID.
         */
        fun createIntent(context: Context, batteryStyle: BatteryStyle, categoryId: String): Intent {
            return Intent(context, EmojiCustomizeActivity::class.java).apply {
                putExtra(EXTRA_BATTERY_STYLE, batteryStyle)
                putExtra(EXTRA_CATEGORY_ID, categoryId)
            }
        }
    }
    
    // ViewBinding
    override val binding by lazy { ActivityEmojiCustomizeBinding.inflate(layoutInflater) }
    
    // ViewModel
    private val viewModel: CustomizeViewModel by viewModels()
    
    // Dependencies
    @Inject
    override lateinit var appRepository: AppRepository
    
    @Inject
    lateinit var emojiUnifiedToggleManager: com.tqhit.battery.one.features.emoji.presentation.overlay.permission.EmojiUnifiedToggleManager
    
    @Inject
    lateinit var emojiAccessibilityPermissionTrackerService: com.tqhit.battery.one.features.emoji.presentation.overlay.permission.EmojiAccessibilityPermissionTrackerService
    
    // Adapters - Phase 1 specialized adapters for component-specific display
    private lateinit var batteryComponentAdapter: BatteryComponentAdapter
    private lateinit var emojiComponentAdapter: EmojiComponentAdapter
    
    // State
    private var initialBatteryStyle: BatteryStyle? = null
    private var categoryId: String? = null
    
    // Modern back handling
    private val backPressedCallback = object : OnBackPressedCallback(true) {
        override fun handleOnBackPressed() {
            Log.d(TAG, "EMOJI_NAVIGATION: OnBackPressedCallback triggered at ${System.currentTimeMillis()}")
            handleBackNavigation()
        }
    }
    
    override fun onCreate(savedInstanceState: Bundle?) {
        // CRITICAL: Extract intent data FIRST, before calling super.onCreate()
        // This ensures the data is available when setupData() is called from the base class
        initialBatteryStyle = intent.getSerializableExtra(EXTRA_BATTERY_STYLE) as? BatteryStyle
        categoryId = intent.getStringExtra(EXTRA_CATEGORY_ID)
        Log.d(TAG, "EmojiCustomizeActivity created with style: ${initialBatteryStyle?.name}, categoryId: $categoryId")

        super.onCreate(savedInstanceState)

        // Apply theme before setting content view
        ThemeManager.applyTheme(this)

        // Set content view
        setContentView(binding.root)

        // Setup modern back handling for API 33+
        setupBackHandling()

        // Setup UI
        setupActionBar()
        setupUI()
    }
    
    override fun setupData() {
        super.setupData()
        Log.d(TAG, "EmojiCustomizeActivity setupData called")

        setupUIComponents()
        setupRecyclerViews()
        setupClickListeners()
        setupSliders()
        observeViewModel()

        // Initialize with the provided battery style and category ID, or load initial data
        initialBatteryStyle?.let { style ->
            categoryId?.let { catId ->
                Log.d(TAG, "Initializing with category and style: $catId, ${style.name}")
                viewModel.handleEvent(CustomizeEvent.InitializeWithCategoryAndStyle(catId, style))
            } ?: run {
                Log.d(TAG, "Initializing with style only: ${style.name}")
                viewModel.handleEvent(CustomizeEvent.InitializeWithStyle(style))
            }
        } ?: run {
            Log.d(TAG, "Loading initial data")
            viewModel.handleEvent(CustomizeEvent.LoadInitialData)
        }
    }
    
    override fun onResume() {
        super.onResume()
        
        // Initialize unified toggle manager on first resume
        emojiUnifiedToggleManager.initialize(this)
        
        viewModel.handleEvent(CustomizeEvent.OnResume)

        // Check if permissions were granted while away from the app
        if (EmojiOverlayPermissionManager.hasAllRequiredPermissions(this)) {
            Log.d(TAG, "EMOJI_PERMISSION: Permissions now granted in Activity, enabling feature")
            viewModel.handlePermissionResult(granted = true)
        } else {
            Log.d(TAG, "EMOJI_PERMISSION: Permissions still missing in Activity")
            // Permission status will be logged by the permission manager
            val permissionStatus = EmojiOverlayPermissionManager.getPermissionStatusSummary(this)
            Log.d(TAG, "EMOJI_PERMISSION: Current permission status: $permissionStatus")
        }
        
        // Synchronize toggle state with permission tracker
        synchronizeToggleState()
    }
    
    override fun onPause() {
        super.onPause()
        viewModel.handleEvent(CustomizeEvent.OnPause)
    }

    /**
     * Synchronizes toggle state with the unified toggle manager.
     * Called on resume to ensure UI reflects actual permission status.
     */
    private fun synchronizeToggleState() {
        try {
            val managerToggleState = emojiUnifiedToggleManager.getCurrentToggleState()
            val permissionStatus = emojiAccessibilityPermissionTrackerService.getCurrentPermissionStatus()
            
            Log.d(TAG, "EMOJI_TOGGLE: Synchronizing toggle state in Activity - manager: $managerToggleState, permission: $permissionStatus")
            
            // Update UI toggle to reflect actual state
            updateToggleUI(managerToggleState && permissionStatus)
            
        } catch (e: Exception) {
            Log.e(TAG, "Error synchronizing toggle state in Activity", e)
        }
    }

    /**
     * Updates toggle state based on permission changes.
     * Called when permission tracker service detects permission changes.
     */
    private fun updateToggleStateFromPermission(isPermissionGranted: Boolean) {
        try {
            Log.d(TAG, "EMOJI_PERMISSION: Updating toggle state from permission change in Activity: $isPermissionGranted")
            
            if (isPermissionGranted) {
                // Permission granted - check if toggle should be enabled
                val currentToggleState = emojiUnifiedToggleManager.getCurrentToggleState()
                if (currentToggleState) {
                    Log.d(TAG, "EMOJI_PERMISSION: Permission granted and toggle should be enabled in Activity")
                    updateToggleUI(true)
                }
            } else {
                // Permission lost - disable toggle
                Log.d(TAG, "EMOJI_PERMISSION: Permission lost, disabling toggle in Activity")
                updateToggleUI(false)
            }
        } catch (e: Exception) {
            Log.e(TAG, "Error updating toggle state from permission in Activity", e)
        }
    }

    /**
     * Updates the UI toggle switch to reflect the actual toggle state.
     *
     * IMPORTANT: This method only updates the visual state without touching the listener.
     * The listener is set up once in setupClickListeners() and should never be overridden.
     * This follows proper MVI pattern where the UI is a passive observer of state.
     */
    private fun updateToggleUI(isEnabled: Boolean) {
        try {
            val beforeState = binding.globalToggleSwitch.isChecked

            Log.d(TAG, "EMOJI_TOGGLE: Updating UI toggle in Activity to: $isEnabled")
            Log.i("EMOJI_STATE_SYNC", "🎯 ACTIVITY_UPDATE_UI: Before=$beforeState, Target=$isEnabled")

            // Temporarily remove listener to prevent infinite loop during state update
            val currentListener = binding.globalToggleSwitch.onCheckedChangeListener
            Log.d("EMOJI_STATE_SYNC", "🔧 ACTIVITY_LISTENER: Temporarily removing listener to prevent loops")
            binding.globalToggleSwitch.setOnCheckedChangeListener(null)

            // Update toggle visual state
            binding.globalToggleSwitch.isChecked = isEnabled
            val afterState = binding.globalToggleSwitch.isChecked

            // Restore the original listener (don't create a new one)
            binding.globalToggleSwitch.setOnCheckedChangeListener(currentListener)
            Log.d("EMOJI_STATE_SYNC", "🔧 ACTIVITY_LISTENER: Original listener restored (not overridden)")

            Log.i("EMOJI_STATE_SYNC", "✅ ACTIVITY_UI_UPDATED: $beforeState → $afterState (target: $isEnabled)")

            if (afterState == isEnabled) {
                Log.i("EMOJI_STATE_SYNC", "🎯 ACTIVITY_SUCCESS: UI toggle correctly shows $isEnabled")
            } else {
                Log.e("EMOJI_STATE_SYNC", "❌ ACTIVITY_ERROR: UI toggle shows $afterState but should show $isEnabled")
            }

        } catch (e: Exception) {
            Log.e(TAG, "Error updating toggle UI in Activity", e)
            Log.e("EMOJI_STATE_SYNC", "💥 ACTIVITY_ERROR: Failed to update UI toggle", e)
        }
    }
    
    override fun onDestroy() {
        // Clean up unified toggle manager
        try {
            emojiUnifiedToggleManager.cleanup(this)
        } catch (e: Exception) {
            Log.e(TAG, "Error cleaning up unified toggle manager in Activity", e)
        }
        
        super.onDestroy()
    }
    
    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        Log.d(TAG, "EMOJI_NAVIGATION: onOptionsItemSelected called with itemId: ${item.itemId}")
        return when (item.itemId) {
            android.R.id.home -> {
                Log.d(TAG, "EMOJI_NAVIGATION: Back navigation clicked from toolbar at ${System.currentTimeMillis()}")
                handleBackNavigation()
                Log.d(TAG, "EMOJI_NAVIGATION: Returning true from onOptionsItemSelected")
                true
            }
            else -> {
                Log.d(TAG, "EMOJI_NAVIGATION: Delegating to super.onOptionsItemSelected for itemId: ${item.itemId}")
                super.onOptionsItemSelected(item)
            }
        }
    }
    
    override fun onBackPressed() {
        Log.d(TAG, "EMOJI_NAVIGATION: onBackPressed() called at ${System.currentTimeMillis()}")
        
        // For API 33+, the OnBackPressedCallback should handle this
        // For older APIs, handle directly
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.TIRAMISU) {
            // Modern Android - OnBackPressedCallback should be triggered
            Log.d(TAG, "EMOJI_NAVIGATION: Modern Android (API 33+), delegating to OnBackPressedCallback")
            super.onBackPressed() // This triggers the registered callback
        } else {
            // Legacy Android - handle directly
            Log.d(TAG, "EMOJI_NAVIGATION: Legacy Android (API < 33), handling directly")
            handleBackNavigation()
        }
        
        Log.d(TAG, "EMOJI_NAVIGATION: onBackPressed() completed")
    }
    
    /**
     * Sets up modern back handling for API 33+ and compatibility for older versions
     */
    private fun setupBackHandling() {
        Log.d(TAG, "EMOJI_NAVIGATION: Setting up back handling for API ${Build.VERSION.SDK_INT}")
        
        // Register OnBackPressedCallback for all API levels
        // This provides consistent behavior and modern gesture support
        onBackPressedDispatcher.addCallback(this, backPressedCallback)
        
        Log.d(TAG, "EMOJI_NAVIGATION: OnBackPressedCallback registered successfully")
    }

    /**
     * Centralized back navigation handling with immediate finish
     */
    private fun handleBackNavigation() {
        Log.d(TAG, "EMOJI_NAVIGATION: handleBackNavigation called, isFinishing: $isFinishing")
        
        if (!isFinishing) {
            Log.d(TAG, "EMOJI_NAVIGATION: Finishing activity immediately")
            // Clear ViewModel state for consistency but don't wait for it
            viewModel.handleEvent(CustomizeEvent.NavigateBack)
            // Finish immediately to prevent double-tap issues
            finish()
            Log.d(TAG, "EMOJI_NAVIGATION: Activity.finish() called")
        } else {
            Log.d(TAG, "EMOJI_NAVIGATION: Activity already finishing, skipping navigation")
        }
    }
    
    /**
     * Sets up the ActionBar/Toolbar with proper theme integration and properly sized back arrow
     */
    private fun setupActionBar() {
        Log.d(TAG, "EMOJI_NAVIGATION: Setting up ActionBar")
        
        setSupportActionBar(binding.toolbar)
        supportActionBar?.apply {
            setDisplayHomeAsUpEnabled(true)
            setDisplayShowHomeEnabled(true)
            title = getString(R.string.customize_emoji_battery)
            Log.d(TAG, "EMOJI_NAVIGATION: ActionBar configured with home button enabled")
        }
        
        // Apply theme-aware colors to toolbar
        val currentTheme = ThemeManager.getSelectedTheme()
        val isDarkTheme = when (currentTheme) {
            "AutoTheme" -> {
                val mode = resources.configuration.uiMode and 
                    android.content.res.Configuration.UI_MODE_NIGHT_MASK
                mode == android.content.res.Configuration.UI_MODE_NIGHT_YES
            }
            "BlackTheme", "BlackThemeInverted", "AmoledTheme", "AmoledThemeInverted" -> true
            else -> false
        }
        
        // Apply theme-aware color to the ActionBar-controlled navigation icon
        try {
            val upArrow = androidx.appcompat.content.res.AppCompatResources.getDrawable(
                this,
                androidx.appcompat.R.drawable.abc_ic_ab_back_material
            )
            upArrow?.setTint(
                if (isDarkTheme) {
                    getColor(android.R.color.white)
                } else {
                    getColor(android.R.color.black)
                }
            )
            supportActionBar?.setHomeAsUpIndicator(upArrow)
            Log.d(TAG, "EMOJI_NAVIGATION: Set theme-aware ActionBar navigation icon")
        } catch (exception: Exception) {
            Log.e(TAG, "EMOJI_NAVIGATION: Error setting ActionBar navigation icon", exception)
        }
        
        Log.d(TAG, "EMOJI_NAVIGATION: ActionBar setup completed, navigation handled by onOptionsItemSelected")
    }
    
    /**
     * Sets up UI components and their initial states
     */
    private fun setupUIComponents() {
        Log.d(TAG, "Setting up UI components")

        // Initialize specialized adapters for Phase 1 component-specific display
        batteryComponentAdapter = BatteryComponentAdapter(
            parentContext = this,
            onBatteryComponentClick = { style ->
                Log.d(TAG, "EMOJI_CUSTOMIZE_ACTIVITY: Battery component selected: ${style.name}")
                viewModel.handleEvent(CustomizeEvent.SelectBatteryStyle(style))
            },
            onPremiumUnlock = { style ->
                Log.d(TAG, "EMOJI_CUSTOMIZE_ACTIVITY: Premium unlock requested for battery component: ${style.name}")
                // Handle premium unlock if needed
            }
        )

        emojiComponentAdapter = EmojiComponentAdapter(
            parentContext = this,
            onEmojiComponentClick = { style ->
                Log.d(TAG, "EMOJI_CUSTOMIZE_ACTIVITY: Emoji component selected: ${style.name}")
                viewModel.handleEvent(CustomizeEvent.SelectEmojiStyle(style))
            },
            onPremiumUnlock = { style ->
                Log.d(TAG, "EMOJI_CUSTOMIZE_ACTIVITY: Premium unlock requested for emoji component: ${style.name}")
                // Handle premium unlock if needed
            }
        )

        Log.d(TAG, "EMOJI_CUSTOMIZE_ACTIVITY: Specialized adapters initialized successfully")
    }
    
    /**
     * Sets up RecyclerViews for style selection using specialized component adapters
     */
    private fun setupRecyclerViews() {
        Log.d(TAG, "EMOJI_CUSTOMIZE_ACTIVITY: Setting up RecyclerViews with specialized adapters")

        // Battery components RecyclerView - displays only battery container images
        binding.batteryStylesRecyclerView.apply {
            layoutManager = LinearLayoutManager(this@EmojiCustomizeActivity, LinearLayoutManager.HORIZONTAL, false)
            adapter = batteryComponentAdapter
            Log.d(TAG, "EMOJI_CUSTOMIZE_ACTIVITY: Battery component RecyclerView configured")
        }

        // Emoji components RecyclerView - displays only emoji character images
        binding.emojiStylesRecyclerView.apply {
            layoutManager = LinearLayoutManager(this@EmojiCustomizeActivity, LinearLayoutManager.HORIZONTAL, false)
            adapter = emojiComponentAdapter
            Log.d(TAG, "EMOJI_CUSTOMIZE_ACTIVITY: Emoji component RecyclerView configured")
        }

        Log.d(TAG, "EMOJI_CUSTOMIZE_ACTIVITY: RecyclerViews setup completed with specialized adapters")
    }
    
    /**
     * Sets up click listeners for UI components
     */
    private fun setupClickListeners() {
        // Info button
        binding.customizeInfo.setOnClickListener {
            Log.d(TAG, "Info button clicked")
            showInfoDialog()
        }
        
        // Global toggle switch - now uses unified toggle manager
        binding.globalToggleSwitch.setOnCheckedChangeListener { _, isChecked ->
            Log.d(TAG, "EMOJI_TOGGLE: Global toggle switch clicked in Activity - isChecked=$isChecked")
            Log.i("EMOJI_TOGGLE_FLOW", "👆 ACTIVITY_USER_TAP: User tapped toggle to $isChecked")
            Log.d("EMOJI_STATE_SYNC", "🎯 ACTIVITY_LISTENER: Toggle listener triggered, delegating to unified manager")

            // Use unified toggle manager for consistent behavior
            emojiUnifiedToggleManager.handleEmojiToggleChange(
                isEnabled = isChecked,
                context = this,
                callbacks = object : com.tqhit.battery.one.features.emoji.presentation.overlay.permission.EmojiUnifiedToggleManager.ToggleCallbacks {
                    override fun onToggleEnabled() {
                        Log.d(TAG, "EMOJI_TOGGLE: Toggle enabled callback in Activity")
                        Log.i("EMOJI_TOGGLE_FLOW", "✅ ACTIVITY_CALLBACK: onToggleEnabled() - feature successfully enabled")
                        // Update ViewModel state through existing event system
                        viewModel.handlePermissionResult(granted = true)
                    }

                    override fun onToggleDisabled() {
                        Log.d(TAG, "EMOJI_TOGGLE: Toggle disabled callback in Activity")
                        Log.i("EMOJI_TOGGLE_FLOW", "❌ ACTIVITY_CALLBACK: onToggleDisabled() - feature disabled")
                        // Update ViewModel state through existing event system
                        viewModel.handlePermissionResult(granted = false)
                    }

                    override fun onPermissionDialogShown() {
                        Log.d(TAG, "EMOJI_TOGGLE: Permission dialog shown callback in Activity")
                        Log.i("EMOJI_PERMISSION_DIALOG", "🔐 ACTIVITY_CALLBACK: onPermissionDialogShown() - dialog displayed to user")
                        // Optional: Update UI to indicate dialog is being shown
                    }
                }
            )
        }
        
        // Toggle switches
        binding.showEmojiSwitch.setOnCheckedChangeListener { _, isChecked ->
            Log.d(TAG, "Show emoji toggle changed: $isChecked")
            viewModel.handleEvent(CustomizeEvent.ToggleShowEmoji(isChecked))
        }
        
        binding.showPercentageSwitch.setOnCheckedChangeListener { _, isChecked ->
            Log.d(TAG, "Show percentage toggle changed: $isChecked")
            viewModel.handleEvent(CustomizeEvent.ToggleShowPercentage(isChecked))
        }
        
        // Apply button - now handles both regular and premium flows
        binding.applyButton.setOnClickListener {
            Log.d(TAG, "Apply button clicked")
            Log.d(TAG, "VISUAL_INCONSISTENCY_DEBUG: ========== APPLY BUTTON PRESSED ==========")
            Log.d(TAG, "VISUAL_INCONSISTENCY_DEBUG: User pressed Apply Customization button")

            val currentState = viewModel.uiState.value
            if (currentState.hasAnyPremiumSelection) {
                Log.d(TAG, "PREMIUM_FLOW: Premium items selected, triggering reward ad flow")
                viewModel.handleEvent(CustomizeEvent.ApplyPremiumCustomization)
            } else {
                Log.d(TAG, "REGULAR_FLOW: Free items selected, triggering regular apply")
                viewModel.handleEvent(CustomizeEvent.ApplyCustomization)
            }
        }
        
        // Retry button
        binding.retryButton.setOnClickListener {
            Log.d(TAG, "Retry button clicked")
            viewModel.handleEvent(CustomizeEvent.RetryLoad)
        }
    }
    
    /**
     * Shows info dialog explaining the customization features
     */
    private fun showInfoDialog() {
        // TODO: Implement info dialog
        Log.d(TAG, "Info dialog would be shown here")
    }

    /**
     * Sets up sliders for customization controls
     */
    private fun setupSliders() {
        Log.d(TAG, "Setting up sliders")

        // Font size slider (5dp to 40dp)
        binding.fontSizeSlider.setOnSeekBarChangeListener(object : SeekBar.OnSeekBarChangeListener {
            override fun onProgressChanged(seekBar: SeekBar?, progress: Int, fromUser: Boolean) {
                if (fromUser) {
                    val fontSize = progress + 5 // Convert 0-35 to 5-40
                    binding.fontSizeValue.text = "${fontSize}dp"
                    viewModel.handleEvent(CustomizeEvent.UpdatePercentageFontSize(fontSize))
                    Log.d(TAG, "Font size changed to: ${fontSize}dp")
                }
            }

            override fun onStartTrackingTouch(seekBar: SeekBar?) {}
            override fun onStopTrackingTouch(seekBar: SeekBar?) {}
        })

        // Emoji scale slider (0.5x to 2.0x)
        binding.emojiScaleSlider.setOnSeekBarChangeListener(object : SeekBar.OnSeekBarChangeListener {
            override fun onProgressChanged(seekBar: SeekBar?, progress: Int, fromUser: Boolean) {
                if (fromUser) {
                    val scale = 0.5f + (progress * 0.1f) // Convert 0-15 to 0.5-2.0
                    binding.emojiScaleValue.text = String.format("%.1fx", scale)
                    viewModel.handleEvent(CustomizeEvent.UpdateEmojiSizeScale(scale))
                    Log.d(TAG, "Emoji scale changed to: ${scale}x")
                }
            }

            override fun onStartTrackingTouch(seekBar: SeekBar?) {}
            override fun onStopTrackingTouch(seekBar: SeekBar?) {}
        })

        // Preview level slider
        binding.previewLevelSlider.setOnSeekBarChangeListener(object : SeekBar.OnSeekBarChangeListener {
            override fun onProgressChanged(seekBar: SeekBar?, progress: Int, fromUser: Boolean) {
                if (fromUser) {
                    binding.previewLevelText.text = "${progress}%"
                    viewModel.handleEvent(CustomizeEvent.UpdatePreviewBatteryLevel(progress))
                    Log.d(TAG, "Preview level changed to: ${progress}%")
                }
            }

            override fun onStartTrackingTouch(seekBar: SeekBar?) {}
            override fun onStopTrackingTouch(seekBar: SeekBar?) {}
        })
    }

    /**
     * Observes ViewModel state and effects - refactored for new MVI architecture
     */
    private fun observeViewModel() {
        Log.d(TAG, "REFACTORED_ACTIVITY: Setting up ViewModel observers with new state and effects")

        // Observe UI state changes
        lifecycleScope.launch {
            repeatOnLifecycle(Lifecycle.State.STARTED) {
                Log.d(TAG, "REFACTORED_ACTIVITY: Starting to collect UI state from ViewModel")
                viewModel.uiState.collect { state ->
                    Log.d(TAG, "REFACTORED_ACTIVITY: UI state updated - loading=${state.isLoading}, selectedStyle=${state.selectedStyle?.name}")
                    Log.d(TAG, "REFACTORED_ACTIVITY: State details - hasError=${state.hasError}, canApplyChanges=${state.canApplyChanges}")
                    updateUI(state)
                }
            }
        }

        // Observe effects separately - NEW: Handle effects instead of navigation/permission flags in state
        lifecycleScope.launch {
            repeatOnLifecycle(Lifecycle.State.STARTED) {
                Log.d(TAG, "REFACTORED_ACTIVITY: Starting to collect effects from ViewModel")
                viewModel.effects.collect { effect ->
                    Log.d(TAG, "REFACTORED_ACTIVITY: Effect received: ${effect::class.simpleName}")
                    handleEffect(effect)
                }
            }
        }

        // Observe permission tracker service for automatic toggle updates
        lifecycleScope.launch {
            repeatOnLifecycle(Lifecycle.State.STARTED) {
                Log.d(TAG, "EMOJI_PERMISSION: Starting to observe permission tracker service in Activity")
                emojiAccessibilityPermissionTrackerService.permissionStatus.collect { isPermissionGranted ->
                    Log.d(TAG, "EMOJI_PERMISSION: Permission status changed to: $isPermissionGranted in Activity")
                    // Auto-update toggle when permission status changes (user returns from settings)
                    updateToggleStateFromPermission(isPermissionGranted)
                }
            }
        }

        // Observe unified toggle manager state for toggle synchronization
        lifecycleScope.launch {
            repeatOnLifecycle(Lifecycle.State.STARTED) {
                Log.d(TAG, "EMOJI_TOGGLE: Starting to observe unified toggle manager state in Activity")
                Log.i("EMOJI_STATE_SYNC", "🎯 ACTIVITY_OBSERVER: StateFlow observer started in Activity")

                emojiUnifiedToggleManager.toggleState.collect { isToggleEnabled ->
                    val currentUIState = binding.globalToggleSwitch.isChecked

                    // Comprehensive logging for verification
                    Log.i("EMOJI_STATE_SYNC", "📡 ACTIVITY_RECEIVED: StateFlow emitted $isToggleEnabled")
                    Log.d("EMOJI_TOGGLE_FLOW", "🎯 ACTIVITY_STATE: UI=$currentUIState, Manager=$isToggleEnabled")
                    Log.i("EMOJI_STATE_SYNC", "🔄 ACTIVITY_SYNC: About to update UI toggle to $isToggleEnabled")

                    if (currentUIState != isToggleEnabled) {
                        Log.i("EMOJI_STATE_SYNC", "⚡ ACTIVITY_MISMATCH: UI state ($currentUIState) != Manager state ($isToggleEnabled) - FIXING")
                    } else {
                        Log.d("EMOJI_STATE_SYNC", "✅ ACTIVITY_MATCH: UI state already matches manager state ($isToggleEnabled)")
                    }

                    // Synchronize UI toggle with manager state (this is the critical fix)
                    updateToggleUI(isToggleEnabled)

                    Log.i("EMOJI_STATE_SYNC", "✅ ACTIVITY_COMPLETE: UI toggle synchronized to $isToggleEnabled")
                    Log.d("EMOJI_TOGGLE_FLOW", "🎯 ACTIVITY_FINAL: UI toggle should now show $isToggleEnabled")
                }
            }
        }
    }

    /**
     * Updates the UI based on the current state - refactored for new CustomizeUiState
     */
    private fun updateUI(state: CustomizeUiState) {
        try {
            Log.d(TAG, "REFACTORED_ACTIVITY: updateUI called with CustomizeUiState")
            Log.d(TAG, "REFACTORED_ACTIVITY: - isLoading: ${state.isLoading}")
            Log.d(TAG, "REFACTORED_ACTIVITY: - hasError: ${state.hasError}")
            Log.d(TAG, "REFACTORED_ACTIVITY: - canApplyChanges: ${state.canApplyChanges}")
            Log.d(TAG, "REFACTORED_ACTIVITY: - selectedBatteryStyle: ${state.selectedBatteryStyle?.name}")
            Log.d(TAG, "REFACTORED_ACTIVITY: - selectedEmojiStyle: ${state.selectedEmojiStyle?.name}")

            updateLoadingState(state)
            updateErrorState(state)
            updatePreview(state)
            updateStyleSelections(state)
            updateCustomizationControls(state)
            updateApplyButton(state)

            Log.d(TAG, "REFACTORED_ACTIVITY: updateUI completed successfully")
        } catch (exception: Exception) {
            Log.e(TAG, "REFACTORED_ACTIVITY: Error updating UI", exception)
        }
    }

    /**
     * Handles effects from the ViewModel - NEW: Replaces navigation and permission flag handling
     */
    private fun handleEffect(effect: CustomizeEffect) {
        Log.d(TAG, "REFACTORED_ACTIVITY: Handling effect: ${effect::class.simpleName}")

        when (effect) {
            is CustomizeEffect.Navigation.NavigateBack -> {
                Log.d(TAG, "REFACTORED_ACTIVITY: Navigating back")
                handleBackNavigation()
            }
            is CustomizeEffect.UserFeedback.ShowSuccessMessage -> {
                Log.d(TAG, "REFACTORED_ACTIVITY: Showing success message: ${effect.message}")
                showSuccessMessage(effect.message)
            }
            is CustomizeEffect.UserFeedback.ShowErrorMessage -> {
                Log.d(TAG, "REFACTORED_ACTIVITY: Showing error message: ${effect.message}")
                showErrorMessage(effect.message)
            }
            is CustomizeEffect.UserFeedback.ShowToast -> {
                Log.d(TAG, "REFACTORED_ACTIVITY: Showing toast: ${effect.message}")
                showToastMessage(effect.message)
            }
            is CustomizeEffect.UserFeedback.ShowValidationError -> {
                Log.d(TAG, "REFACTORED_ACTIVITY: Showing validation error: ${effect.message}")
                showValidationError(effect.message)
            }
            is CustomizeEffect.SystemInteraction.RequestAccessibilityPermissions -> {
                Log.d(TAG, "REFACTORED_ACTIVITY: Requesting accessibility permissions")
                requestAccessibilityPermissions()
            }
            is CustomizeEffect.SystemInteraction.OpenAccessibilitySettings -> {
                Log.d(TAG, "REFACTORED_ACTIVITY: Opening accessibility settings")
                openAccessibilitySettings()
            }
            is CustomizeEffect.SystemInteraction.ShowRewardAd -> {
                Log.d(TAG, "REFACTORED_ACTIVITY: Showing reward ad for premium customization")
                handleShowRewardAd(effect.customizationConfig)
            }
            is CustomizeEffect.UIInteraction.ShowColorPicker -> {
                Log.d(TAG, "REFACTORED_ACTIVITY: Showing color picker")
                // Color picker is handled in UI state, no additional action needed
            }
            is CustomizeEffect.UIInteraction.HideColorPicker -> {
                Log.d(TAG, "REFACTORED_ACTIVITY: Hiding color picker")
                // Color picker is handled in UI state, no additional action needed
            }
            is CustomizeEffect.UIInteraction.AnimateApplySuccess -> {
                Log.d(TAG, "REFACTORED_ACTIVITY: Animating apply success")
                animateApplySuccess()
            }
            is CustomizeEffect.UIInteraction.AnimateApplyError -> {
                Log.d(TAG, "REFACTORED_ACTIVITY: Animating apply error")
                animateApplyError()
            }
            else -> {
                Log.d(TAG, "REFACTORED_ACTIVITY: Unhandled effect: ${effect::class.simpleName}")
            }
        }
    }

    /**
     * Handles showing reward ad for premium customization
     */
    private fun handleShowRewardAd(customizationConfig: CustomizationConfig) {
        Log.d(TAG, "PREMIUM_FLOW: Starting reward ad flow for premium customization")
        Log.d(TAG, "PREMIUM_FLOW: Battery: ${customizationConfig.batteryStyleName}, Emoji: ${customizationConfig.emojiStyleName}")

        // For now, show a placeholder Toast message
        // TODO: Replace with actual reward ad implementation
        showToastMessage("Reward ad would show here for premium item")

        // Simulate ad completion and apply the customization
        // In a real implementation, this would be called after the ad completes successfully
        handleRewardAdCompleted(customizationConfig)
    }

    /**
     * Handles reward ad completion and applies the customization
     */
    private fun handleRewardAdCompleted(customizationConfig: CustomizationConfig) {
        Log.d(TAG, "PREMIUM_FLOW: Reward ad completed, applying premium customization")

        // Use the specialized EffectHandler method that bypasses premium checks
        // since the user has "earned" access via the reward ad
        lifecycleScope.launch {
            viewModel.applyCustomizationAfterRewardAd(customizationConfig)
        }
    }

    /**
     * Updates loading state visibility - refactored for new CustomizeUiState
     */
    private fun updateLoadingState(state: CustomizeUiState) {
        binding.loadingContainer.visibility = if (state.isAnyLoading) {
            android.view.View.VISIBLE
        } else {
            android.view.View.GONE
        }

        // Hide main content while loading
        binding.scrollView.visibility = if (state.isAnyLoading) {
            android.view.View.GONE
        } else {
            android.view.View.VISIBLE
        }
    }

    /**
     * Updates error state visibility and message - refactored for new CustomizeUiState
     */
    private fun updateErrorState(state: CustomizeUiState) {
        binding.errorContainer.visibility = if (state.hasError) {
            android.view.View.VISIBLE
        } else {
            android.view.View.GONE
        }

        if (state.hasError) {
            binding.errorMessage.text = state.errorMessage ?: getString(R.string.error_loading_customization)
        }

        // Hide main content when showing error
        binding.scrollView.visibility = if (state.hasError) {
            android.view.View.GONE
        } else {
            android.view.View.VISIBLE
        }
    }

    /**
     * Updates the live preview based on current state - refactored for new CustomizeUiState
     */
    private fun updatePreview(state: CustomizeUiState) {
        Log.d(TAG, "EMOJI_SELECTION_FLOW: updatePreview called")
        Log.d(TAG, "EMOJI_SELECTION_FLOW: Preview state - showEmoji=${state.showEmojiToggle}, showPercentage=${state.showPercentageToggle}")
        Log.d(TAG, "EMOJI_SELECTION_FLOW: Preview state - batteryLevel=${state.previewBatteryLevel}%")

        // Update preview percentage text
        binding.previewPercentage.apply {
            text = "${state.previewBatteryLevel}%"
            visibility = if (state.showPercentageToggle) {
                android.view.View.VISIBLE
            } else {
                android.view.View.GONE
            }
        }

        // Update emoji visibility
        binding.previewEmoji.visibility = if (state.showEmojiToggle) {
            android.view.View.VISIBLE
        } else {
            android.view.View.GONE
        }

        // Load images using composite state for true mix-and-match
        loadCompositePreviewImages(state)
    }

    /**
     * Loads preview images using composite state for true mix-and-match functionality.
     * Supports both Phase 1 (single style) and Phase 2 (mix-and-match) modes.
     */
    private fun loadCompositePreviewImages(state: CustomizeUiState) {
        Log.d(TAG, "EMOJI_SELECTION_FLOW: Loading composite preview images")
        Log.d(TAG, "EMOJI_SELECTION_FLOW: ========== PREVIEW IMAGE LOADING STARTED ==========")

        // Add state debugging - log the complete state information to understand why preview might show defaults
        Log.d(TAG, "EMOJI_SELECTION_FLOW: Complete state information:")
        Log.d(TAG, "EMOJI_SELECTION_FLOW: - Selected Style (Phase 1): ${state.selectedStyle?.name} (id=${state.selectedStyle?.id})")
        Log.d(TAG, "EMOJI_SELECTION_FLOW: - Selected Battery Style (Phase 2): ${state.selectedBatteryStyle?.name} (id=${state.selectedBatteryStyle?.id})")
        Log.d(TAG, "EMOJI_SELECTION_FLOW: - Selected Emoji Style (Phase 2): ${state.selectedEmojiStyle?.name} (id=${state.selectedEmojiStyle?.id})")
        Log.d(TAG, "EMOJI_SELECTION_FLOW: - Show Emoji Toggle: ${state.showEmojiToggle}")
        Log.d(TAG, "EMOJI_SELECTION_FLOW: - Show Percentage Toggle: ${state.showPercentageToggle}")

        // Determine which styles to use for preview (Phase 1 vs Phase 2)
        val batteryStyleForPreview = state.selectedBatteryStyle ?: state.selectedStyle
        val emojiStyleForPreview = state.selectedEmojiStyle ?: state.selectedStyle

        Log.d(TAG, "EMOJI_SELECTION_FLOW: Determined styles for preview:")
        Log.d(TAG, "EMOJI_SELECTION_FLOW: - Battery style for preview: ${batteryStyleForPreview?.name}")
        Log.d(TAG, "EMOJI_SELECTION_FLOW: - Emoji style for preview: ${emojiStyleForPreview?.name}")

        // Log selected battery style details (ID, name, all image URLs)
        batteryStyleForPreview?.let { batteryStyle ->
            Log.d(TAG, "EMOJI_SELECTION_FLOW: Battery Style Details:")
            Log.d(TAG, "EMOJI_SELECTION_FLOW:   - ID: ${batteryStyle.id}")
            Log.d(TAG, "EMOJI_SELECTION_FLOW:   - Name: ${batteryStyle.name}")
            Log.d(TAG, "EMOJI_SELECTION_FLOW:   - Category: ${batteryStyle.category.displayName}")
            Log.d(TAG, "EMOJI_SELECTION_FLOW:   - Gallery Thumbnail URL: '${batteryStyle.galleryThumbnailUrl}'")
            Log.d(TAG, "EMOJI_SELECTION_FLOW:   - Customize Preview URL: '${batteryStyle.customizePreviewUrl}'")
            Log.d(TAG, "EMOJI_SELECTION_FLOW:   - Battery Image URL: '${batteryStyle.batteryImageUrl}'")
            Log.d(TAG, "EMOJI_SELECTION_FLOW:   - Emoji Image URL: '${batteryStyle.emojiImageUrl}'")
        } ?: Log.w(TAG, "EMOJI_SELECTION_FLOW: No battery style available for preview")

        // Log selected emoji style details (ID, name, all image URLs)
        emojiStyleForPreview?.let { emojiStyle ->
            Log.d(TAG, "EMOJI_SELECTION_FLOW: Emoji Style Details:")
            Log.d(TAG, "EMOJI_SELECTION_FLOW:   - ID: ${emojiStyle.id}")
            Log.d(TAG, "EMOJI_SELECTION_FLOW:   - Name: ${emojiStyle.name}")
            Log.d(TAG, "EMOJI_SELECTION_FLOW:   - Category: ${emojiStyle.category.displayName}")
            Log.d(TAG, "EMOJI_SELECTION_FLOW:   - Gallery Thumbnail URL: '${emojiStyle.galleryThumbnailUrl}'")
            Log.d(TAG, "EMOJI_SELECTION_FLOW:   - Customize Preview URL: '${emojiStyle.customizePreviewUrl}'")
            Log.d(TAG, "EMOJI_SELECTION_FLOW:   - Battery Image URL: '${emojiStyle.batteryImageUrl}'")
            Log.d(TAG, "EMOJI_SELECTION_FLOW:   - Emoji Image URL: '${emojiStyle.emojiImageUrl}'")
        } ?: Log.w(TAG, "EMOJI_SELECTION_FLOW: No emoji style available for preview")

        // Add debugging for ImageView dimensions
        binding.previewBattery.post {
            Log.d(TAG, "PREVIEW_DEBUG: Battery ImageView - Width: ${binding.previewBattery.width}, Height: ${binding.previewBattery.height}")
            Log.d(TAG, "PREVIEW_DEBUG: Container - Width: ${binding.previewContainer.width}, Height: ${binding.previewContainer.height}")
        }
        
        // Load battery image from determined style (supports both Phase 1 and Phase 2)
        batteryStyleForPreview?.let { batteryStyle ->
            // Add URL validation - check if URLs are not empty before loading
            fun isValidUrl(url: String): Boolean {
                return url.isNotBlank() && (url.startsWith("http://") || url.startsWith("https://"))
            }

            // Fix image URL selection - use selected battery style with fallback logic
            val batteryImageUrl = when {
                isValidUrl(batteryStyle.batteryImageUrl) -> batteryStyle.batteryImageUrl
                isValidUrl(batteryStyle.customizePreviewUrl) -> batteryStyle.customizePreviewUrl
                isValidUrl(batteryStyle.galleryThumbnailUrl) -> batteryStyle.galleryThumbnailUrl
                else -> ""
            }

            // Log which URLs are actually being used for loading
            Log.d(TAG, "EMOJI_SELECTION_FLOW: Battery image URL selection:")
            Log.d(TAG, "EMOJI_SELECTION_FLOW:   - Primary (batteryImageUrl): '${batteryStyle.batteryImageUrl}' (valid: ${isValidUrl(batteryStyle.batteryImageUrl)})")
            Log.d(TAG, "EMOJI_SELECTION_FLOW:   - Fallback 1 (customizePreviewUrl): '${batteryStyle.customizePreviewUrl}' (valid: ${isValidUrl(batteryStyle.customizePreviewUrl)})")
            Log.d(TAG, "EMOJI_SELECTION_FLOW:   - Fallback 2 (galleryThumbnailUrl): '${batteryStyle.galleryThumbnailUrl}' (valid: ${isValidUrl(batteryStyle.galleryThumbnailUrl)})")
            Log.d(TAG, "EMOJI_SELECTION_FLOW:   - Selected URL: '$batteryImageUrl'")

            if (batteryImageUrl.isNotEmpty()) {
                Log.d(TAG, "EMOJI_SELECTION_FLOW: Loading battery image from: $batteryImageUrl")
                Glide.with(this)
                    .load(batteryImageUrl)
                    .override(120, 120) // Force a minimum size
                    .centerInside()
                    .placeholder(R.drawable.ic_battery_placeholder)
                    .error(R.drawable.ic_battery_placeholder)
                    .listener(object : RequestListener<Drawable> {
                        override fun onLoadFailed(
                            e: GlideException?,
                            model: Any?,
                            target: Target<Drawable>,
                            isFirstResource: Boolean
                        ): Boolean {
                            Log.w(TAG, "EMOJI_SELECTION_FLOW: Failed to load battery image: $batteryImageUrl", e)
                            return false
                        }

                        override fun onResourceReady(
                            resource: Drawable,
                            model: Any,
                            target: Target<Drawable>,
                            dataSource: DataSource,
                            isFirstResource: Boolean
                        ): Boolean {
                            Log.d(TAG, "EMOJI_SELECTION_FLOW: Battery image loaded successfully: ${resource.intrinsicWidth}x${resource.intrinsicHeight}")
                            Log.d(TAG, "EMOJI_SELECTION_FLOW: PREVIEW Battery Image Loaded Successfully:")
                            Log.d(TAG, "EMOJI_SELECTION_FLOW: - URL: $batteryImageUrl")
                            Log.d(TAG, "EMOJI_SELECTION_FLOW: - Drawable size: ${resource.intrinsicWidth}x${resource.intrinsicHeight}")
                            Log.d(TAG, "EMOJI_SELECTION_FLOW: - This drawable is displayed in preview ImageView")
                            return false
                        }
                    })
                    .into(binding.previewBattery)
            } else {
                // Fix placeholder logic - ensure placeholders are only used when no valid style is selected
                Log.w(TAG, "EMOJI_SELECTION_FLOW: Battery style selected but no valid image URL found")
                Log.w(TAG, "EMOJI_SELECTION_FLOW: Using placeholder because all image URLs are invalid or empty")
                Glide.with(this)
                    .load(R.drawable.ic_battery_placeholder)
                    .override(120, 120)
                    .centerInside()
                    .into(binding.previewBattery)
            }
        } ?: run {
            // Fix placeholder logic - only use placeholder when no valid style is selected, not when URLs are temporarily empty
            Log.w(TAG, "EMOJI_SELECTION_FLOW: No battery style available for preview, using placeholder")
            Glide.with(this)
                .load(R.drawable.ic_battery_placeholder)
                .override(120, 120)
                .centerInside()
                .into(binding.previewBattery)
        }

        // Load emoji image from determined style (supports both Phase 1 and Phase 2)
        emojiStyleForPreview?.let { emojiStyle ->
            // Add URL validation - check if URLs are not empty before loading
            fun isValidUrl(url: String): Boolean {
                return url.isNotBlank() && (url.startsWith("http://") || url.startsWith("https://"))
            }

            // Fix image URL selection - use selected emoji style with fallback logic
            val emojiImageUrl = when {
                isValidUrl(emojiStyle.emojiImageUrl) -> emojiStyle.emojiImageUrl
                isValidUrl(emojiStyle.customizePreviewUrl) -> emojiStyle.customizePreviewUrl
                isValidUrl(emojiStyle.galleryThumbnailUrl) -> emojiStyle.galleryThumbnailUrl
                else -> ""
            }

            // Log which URLs are actually being used for loading
            Log.d(TAG, "EMOJI_SELECTION_FLOW: Emoji image URL selection:")
            Log.d(TAG, "EMOJI_SELECTION_FLOW:   - Primary (emojiImageUrl): '${emojiStyle.emojiImageUrl}' (valid: ${isValidUrl(emojiStyle.emojiImageUrl)})")
            Log.d(TAG, "EMOJI_SELECTION_FLOW:   - Fallback 1 (customizePreviewUrl): '${emojiStyle.customizePreviewUrl}' (valid: ${isValidUrl(emojiStyle.customizePreviewUrl)})")
            Log.d(TAG, "EMOJI_SELECTION_FLOW:   - Fallback 2 (galleryThumbnailUrl): '${emojiStyle.galleryThumbnailUrl}' (valid: ${isValidUrl(emojiStyle.galleryThumbnailUrl)})")
            Log.d(TAG, "EMOJI_SELECTION_FLOW:   - Selected URL: '$emojiImageUrl'")

            if (emojiImageUrl.isNotEmpty()) {
                Log.d(TAG, "EMOJI_SELECTION_FLOW: Loading emoji image from: $emojiImageUrl")
                Glide.with(this)
                    .load(emojiImageUrl)
                    .override(120, 120) // Force a minimum size
                    .centerInside()
                    .placeholder(R.drawable.ic_emoji_placeholder)
                    .error(R.drawable.ic_emoji_placeholder)
                    .listener(object : RequestListener<Drawable> {
                        override fun onLoadFailed(
                            e: GlideException?,
                            model: Any?,
                            target: Target<Drawable>,
                            isFirstResource: Boolean
                        ): Boolean {
                            Log.w(TAG, "EMOJI_SELECTION_FLOW: Failed to load emoji image: $emojiImageUrl", e)
                            return false
                        }

                        override fun onResourceReady(
                            resource: Drawable,
                            model: Any,
                            target: Target<Drawable>,
                            dataSource: DataSource,
                            isFirstResource: Boolean
                        ): Boolean {
                            Log.d(TAG, "EMOJI_SELECTION_FLOW: Emoji image loaded successfully: ${resource.intrinsicWidth}x${resource.intrinsicHeight}")
                            Log.d(TAG, "EMOJI_SELECTION_FLOW: PREVIEW Emoji Image Loaded Successfully:")
                            Log.d(TAG, "EMOJI_SELECTION_FLOW: - URL: $emojiImageUrl")
                            Log.d(TAG, "EMOJI_SELECTION_FLOW: - Drawable size: ${resource.intrinsicWidth}x${resource.intrinsicHeight}")
                            Log.d(TAG, "EMOJI_SELECTION_FLOW: - This drawable is displayed in preview ImageView")
                            return false
                        }
                    })
                    .into(binding.previewEmoji)
            } else {
                // Fix placeholder logic - ensure placeholders are only used when no valid style is selected
                Log.w(TAG, "EMOJI_SELECTION_FLOW: Emoji style selected but no valid image URL found")
                Log.w(TAG, "EMOJI_SELECTION_FLOW: Using placeholder because all image URLs are invalid or empty")
                Glide.with(this)
                    .load(R.drawable.ic_emoji_placeholder)
                    .override(120, 120)
                    .centerInside()
                    .into(binding.previewEmoji)
            }
        } ?: run {
            // Fix placeholder logic - only use placeholder when no valid style is selected, not when URLs are temporarily empty
            Log.w(TAG, "EMOJI_SELECTION_FLOW: No emoji style available for preview, using placeholder")
            Glide.with(this)
                .load(R.drawable.ic_emoji_placeholder)
                .override(120, 120)
                .centerInside()
                .into(binding.previewEmoji)
        }
    }

    /**
     * Legacy method - kept for backward compatibility during transition.
     * @deprecated Use loadCompositePreviewImages instead for Phase 2 functionality.
     */
    @Deprecated("Use loadCompositePreviewImages for true mix-and-match functionality")
    private fun loadPreviewImages(style: BatteryStyle) {
        // Load emoji image
        if (style.emojiImageUrl.isNotEmpty()) {
            Glide.with(this)
                .load(style.emojiImageUrl)
                .listener(object : RequestListener<Drawable> {
                    override fun onLoadFailed(
                        e: GlideException?,
                        model: Any?,
                        target: Target<Drawable>,
                        isFirstResource: Boolean
                    ): Boolean {
                        Log.w(TAG, "Failed to load emoji image: ${style.emojiImageUrl}", e)
                        return false
                    }

                    override fun onResourceReady(
                        resource: Drawable,
                        model: Any,
                        target: Target<Drawable>,
                        dataSource: com.bumptech.glide.load.DataSource,
                        isFirstResource: Boolean
                    ): Boolean {
                        Log.d(TAG, "Emoji image loaded successfully: ${style.emojiImageUrl}")
                        return false
                    }
                })
                .into(binding.previewEmoji)
        }

        // Load battery image
        if (style.batteryImageUrl.isNotEmpty()) {
            Glide.with(this)
                .load(style.batteryImageUrl)
                .listener(object : RequestListener<Drawable> {
                    override fun onLoadFailed(
                        e: GlideException?,
                        model: Any?,
                        target: Target<Drawable>,
                        isFirstResource: Boolean
                    ): Boolean {
                        Log.w(TAG, "Failed to load battery image: ${style.batteryImageUrl}", e)
                        return false
                    }

                    override fun onResourceReady(
                        resource: Drawable,
                        model: Any,
                        target: Target<Drawable>,
                        dataSource: com.bumptech.glide.load.DataSource,
                        isFirstResource: Boolean
                    ): Boolean {
                        Log.d(TAG, "Battery image loaded successfully: ${style.batteryImageUrl}")
                        return false
                    }
                })
                .into(binding.previewBattery)
        }
    }

    /**
     * Updates style selection RecyclerViews using specialized component adapters - refactored for new CustomizeUiState
     */
    private fun updateStyleSelections(state: CustomizeUiState) {
        Log.d(TAG, "EMOJI_CUSTOMIZE_ACTIVITY: Updating style selections")
        Log.d(TAG, "EMOJI_CUSTOMIZE_ACTIVITY: Battery styles count: ${state.availableBatteryStyles.size}")
        Log.d(TAG, "EMOJI_CUSTOMIZE_ACTIVITY: Emoji styles count: ${state.availableEmojiStyles.size}")

        // Update battery component carousel
        if (state.availableBatteryStyles.isNotEmpty()) {
            Log.d(TAG, "EMOJI_CUSTOMIZE_ACTIVITY: Submitting ${state.availableBatteryStyles.size} battery styles to component adapter")
            batteryComponentAdapter.submitList(state.availableBatteryStyles)
        } else {
            Log.w(TAG, "EMOJI_CUSTOMIZE_ACTIVITY: ===== NO BATTERY STYLES AVAILABLE =====")
        }

        // Update emoji component carousel
        if (state.availableEmojiStyles.isNotEmpty()) {
            Log.d(TAG, "EMOJI_CUSTOMIZE_ACTIVITY: Submitting ${state.availableEmojiStyles.size} emoji styles to component adapter")
            emojiComponentAdapter.submitList(state.availableEmojiStyles)
        } else {
            Log.w(TAG, "EMOJI_CUSTOMIZE_ACTIVITY: ===== NO EMOJI STYLES AVAILABLE =====")
        }

        // Update selection states in adapters
        batteryComponentAdapter.updateSelection(state.selectedBatteryStyle?.id)
        emojiComponentAdapter.updateSelection(state.selectedEmojiStyle?.id)

        Log.d(TAG, "EMOJI_CUSTOMIZE_ACTIVITY: Style selections updated successfully")
    }

    /**
     * Updates customization controls based on state - refactored for new CustomizeUiState
     */
    private fun updateCustomizationControls(state: CustomizeUiState) {
        Log.d(TAG, "REFACTORED_ACTIVITY: updateCustomizationControls called")
        Log.d(TAG, "REFACTORED_ACTIVITY: State values - isGlobalEnabled=${state.isGlobalEnabled}")

        // Update global toggle without triggering listener
        Log.d(TAG, "TOGGLE_DEBUG: Updating global toggle switch state to ${state.isGlobalEnabled}")

        // Use the dedicated updateToggleUI method to preserve the listener
        // This ensures the listener set up in setupClickListeners() is not overridden
        updateToggleUI(state.isGlobalEnabled)

        // Update toggles without triggering listeners
        binding.showEmojiSwitch.setOnCheckedChangeListener(null)
        binding.showEmojiSwitch.isChecked = state.showEmojiToggle
        binding.showEmojiSwitch.setOnCheckedChangeListener { _, isChecked ->
            viewModel.handleEvent(CustomizeEvent.ToggleShowEmoji(isChecked))
        }

        binding.showPercentageSwitch.setOnCheckedChangeListener(null)
        binding.showPercentageSwitch.isChecked = state.showPercentageToggle
        binding.showPercentageSwitch.setOnCheckedChangeListener { _, isChecked ->
            viewModel.handleEvent(CustomizeEvent.ToggleShowPercentage(isChecked))
        }

        // Update sliders without triggering listeners
        binding.fontSizeSlider.setOnSeekBarChangeListener(null)
        binding.fontSizeSlider.progress = state.percentageFontSize - 5 // Convert 5-40 to 0-35
        binding.fontSizeValue.text = "${state.percentageFontSize}dp"
        setupSliders() // Re-attach listeners

        binding.emojiScaleSlider.setOnSeekBarChangeListener(null)
        binding.emojiScaleSlider.progress = ((state.emojiSizeScale - 0.5f) * 10).toInt() // Convert 0.5-2.0 to 0-15
        binding.emojiScaleValue.text = String.format("%.1fx", state.emojiSizeScale)
        setupSliders() // Re-attach listeners
    }

    /**
     * Updates apply button state and appearance based on premium item selection - refactored for new CustomizeUiState
     */
    private fun updateApplyButton(state: CustomizeUiState) {
        // Update button enabled state
        binding.applyButton.isEnabled = !state.isSaving && !state.isLoading && !state.hasError

        // Get references to the new button components
        val buttonIcon = binding.applyButton.findViewById<androidx.appcompat.widget.AppCompatImageView>(R.id.applyButtonIcon)
        val buttonText = binding.applyButton.findViewById<TextView>(R.id.applyButtonText)

        // Update button appearance based on premium selection and state
        when {
            state.isSaving -> {
                // Saving state - hide icon, show saving text
                buttonIcon.visibility = android.view.View.GONE
                buttonText.text = getString(R.string.applying_customization)
                Log.d(TAG, "PREMIUM_BUTTON: Showing saving state")
            }
            state.hasAnyPremiumSelection -> {
                // Premium items selected - show play icon
                buttonIcon.visibility = android.view.View.VISIBLE
                buttonIcon.setImageResource(R.drawable.ic_ad)
                buttonText.text = getString(R.string.apply_customization)
                Log.d(TAG, "PREMIUM_BUTTON: Premium items detected, showing play icon")
                Log.d(TAG, "PREMIUM_BUTTON: Battery premium: ${state.isSelectedBatteryStylePremium}, Emoji premium: ${state.isSelectedEmojiStylePremium}")
            }
            else -> {
                // Free items only - hide icon, normal text
                buttonIcon.visibility = android.view.View.GONE
                buttonText.text = getString(R.string.apply_customization)
                Log.d(TAG, "PREMIUM_BUTTON: Free items only, hiding icon")
            }
        }

        // Apply theme-aware button styling
        val currentTheme = ThemeManager.getSelectedTheme()
        val selectedColor = ThemeManager.getSelectedColor()

        // Set button background based on current theme and color
        binding.applyButton.apply {
            when (selectedColor) {
                "blue" -> setBackgroundColor(getColor(R.color.blue))
                "green" -> setBackgroundColor(getColor(R.color.green))
                "orange" -> setBackgroundColor(getColor(R.color.orange))
                "pink" -> setBackgroundColor(getColor(R.color.pink))
                "red" -> setBackgroundColor(getColor(R.color.red))
                else -> setBackgroundColor(getColor(R.color.blue)) // Default
            }

            // Set text color based on theme
            val isDarkTheme = when (currentTheme) {
                "AutoTheme" -> {
                    val mode = resources.configuration.uiMode and
                        android.content.res.Configuration.UI_MODE_NIGHT_MASK
                    mode == android.content.res.Configuration.UI_MODE_NIGHT_YES
                }
                "BlackTheme", "BlackThemeInverted", "AmoledTheme", "AmoledThemeInverted" -> true
                else -> false
            }

            // Set text color on the TextView component
            buttonText.setTextColor(getColor(android.R.color.white)) // Always white text on colored button
        }
    }

    // NEW: Effect handling methods for the refactored MVI architecture

    /**
     * Shows success message
     */
    private fun showSuccessMessage(message: String) {
        Log.d(TAG, "REFACTORED_ACTIVITY: Showing success message: $message")
        showToastMessage("✓ $message")
    }

    /**
     * Shows error message
     */
    private fun showErrorMessage(message: String) {
        Log.d(TAG, "REFACTORED_ACTIVITY: Showing error message: $message")
        showToastMessage("✗ $message")
    }

    /**
     * Shows toast message
     */
    private fun showToastMessage(message: String) {
        Log.d(TAG, "REFACTORED_ACTIVITY: Showing toast: $message")
        try {
            android.widget.Toast.makeText(this, message, android.widget.Toast.LENGTH_SHORT).show()
        } catch (exception: Exception) {
            Log.e(TAG, "REFACTORED_ACTIVITY: Error showing toast", exception)
        }
    }

    /**
     * Shows validation error
     */
    private fun showValidationError(message: String) {
        Log.d(TAG, "REFACTORED_ACTIVITY: Showing validation error: $message")
        showToastMessage("⚠ $message")
    }

    /**
     * Requests accessibility permissions
     */
    private fun requestAccessibilityPermissions() {
        Log.d(TAG, "REFACTORED_ACTIVITY: Requesting accessibility permissions")
        try {
            EmojiOverlayPermissionManager.checkPermissionsAndProceed(
                context = this,
                onAllPermissionsGranted = {
                    Log.d(TAG, "REFACTORED_ACTIVITY: All permissions granted")
                    viewModel.handlePermissionResult(granted = true)
                },
                onPermissionsDenied = {
                    Log.d(TAG, "REFACTORED_ACTIVITY: Permissions denied")
                    viewModel.handlePermissionResult(granted = false)
                },
                showExplanation = true
            )
        } catch (exception: Exception) {
            Log.e(TAG, "REFACTORED_ACTIVITY: Error requesting permissions", exception)
        }
    }

    /**
     * Opens accessibility settings
     */
    private fun openAccessibilitySettings() {
        Log.d(TAG, "REFACTORED_ACTIVITY: Opening accessibility settings")
        try {
            EmojiOverlayPermissionManager.openAccessibilitySettings(this)
        } catch (exception: Exception) {
            Log.e(TAG, "REFACTORED_ACTIVITY: Error opening accessibility settings", exception)
        }
    }

    /**
     * Animates apply button success
     */
    private fun animateApplySuccess() {
        Log.d(TAG, "REFACTORED_ACTIVITY: Animating apply success")
        // TODO: Implement success animation
    }

    /**
     * Animates apply button error
     */
    private fun animateApplyError() {
        Log.d(TAG, "REFACTORED_ACTIVITY: Animating apply error")
        // TODO: Implement error animation
    }
}
