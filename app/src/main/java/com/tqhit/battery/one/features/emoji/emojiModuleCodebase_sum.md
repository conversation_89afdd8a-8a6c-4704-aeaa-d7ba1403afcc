# Summary of the Emoji Battery Module Codebase

## 1. Overview

This codebase implements a user-facing "Emoji Battery" feature. The goal is to provide a highly customizable battery indicator in the Android status bar, replacing the system default with a dynamic, emoji-based overlay. The module is well-documented with a Product Requirements Document (`emoji-prd.md`) and a completion report for Phase 0, indicating a structured development process.

The feature consists of three main components:
1.  **Gallery Screen (`EmojiBatteryFragment`):** A browsable grid of available battery styles, filterable by categories.
2.  **Customization Screen (`EmojiCustomizeActivity`):** A screen for live-previewing and modifying a selected battery style's appearance (e.g., size, colors, visibility of elements). This has been updated to support mix-and-match customization of battery and emoji components. It now loads all items from the selected category, allowing users to mix and match any battery and emoji from that category.
3.  **Overlay Service (`EmojiBatteryAccessibilityService`):** An accessibility service that draws the custom emoji battery icon over the system's status bar.

## 2. Core Architecture

The module is architected using modern, standard Android development patterns:

*   **Clean Architecture:** The code is well-organized into three distinct layers:
    *   `presentation`: Contains Activities, Fragments, ViewModels, UI State/Events, and Adapters.
    *   `domain`: Contains core business logic, including Models, Repository Interfaces, and Use Cases.
    *   `data`: Contains Repository Implementations and remote/local data sources.
*   **Model-View-Intent (MVI):** The presentation layer uses a sophisticated MVI pattern with a unidirectional data flow. This is implemented with a full suite of MVI components for both the `gallery` and `customize` screens:
    *   **`ViewModel`:** Orchestrates the flow of data and events.
    *   **`StateManager`:** A pure function that takes the current state and an event and returns a new state. This is where the core business logic for state changes resides.
    *   **`EffectHandler`:** Manages side effects like navigation, showing toasts, and making API calls.
    *   **`UiState`:** An immutable data class that represents the entire state of the UI.
    *   **`Event`:** A sealed class that represents all possible user interactions and system events.
    *   **`Effect`:** A sealed class that represents one-time side effects that should not be part of the UI state.
*   **Dependency Injection (DI):** Hilt is used for dependency injection throughout the module, simplifying the management of dependencies like repositories, use cases, and services.

## 3. Key Components by Layer

### Presentation Layer
*   **`EmojiBatteryFragment`:** The main entry point. Displays a grid of `BatteryStyle` items and a list of `EmojiCategory` tabs for filtering. It handles user interactions like category selection and navigation to the `EmojiCustomizeActivity`. It now uses `EmojiCategoryService` to populate the category tabs from Firebase Remote Config.
*   **`EmojiCustomizeActivity`:** Allows users to modify a selected `BatteryStyle`. It features a live preview that updates in real-time and provides controls for various style properties. It now supports mix-and-match customization of battery and emoji components by loading all items from the selected category.
*   **`BatteryGalleryViewModel`:** Manages the state for the gallery screen. It fetches categories and items from the data layer, applies filters, and exposes the final UI state.
*   **Adapters (`BatteryStyleAdapter`, `CategoryAdapter`):** Standard `RecyclerView` adapters for displaying the grid of styles and the horizontal list of categories.
*   **Accessibility Permission Flow:** A new, user-friendly two-step flow has been implemented to guide users through enabling the accessibility service:
    *   **`AccessibilityRationaleDialog`:** A custom dialog that explains why the accessibility service is needed and asks for the user's consent.
    *   **`AccessibilityGuideActivity`:** A new activity that provides a visual step-by-step guide on how to enable the service in the Android settings.
    *   **`EmojiOverlayPermissionManager`:** This manager has been updated to orchestrate the new two-step permission flow.
*   **Unified Toggle Flow:** The global toggle switches in the gallery and customization screens have been unified into a single, consistent, and reactive flow supported by two new components:
    *   **`EmojiAccessibilityPermissionTrackerService`:** A dedicated service to monitor the accessibility permission status for the emoji feature.
    *   **`EmojiUnifiedToggleManager`:** A manager that centralizes the logic for handling the toggle state and the permission flow. It exposes a `StateFlow` that the UI observes, ensuring the toggle's visual state is always in sync with the true permission status.

### Domain Layer
*   **Models:**
    *   **`BatteryStyle`:** The primary domain model representing a complete, displayable style. It is used by the UI. It now includes more image URLs for different purposes (gallery, preview, overlay).
    *   **`EmojiCategory` & `EmojiItem`:** Data models that directly map to the JSON structure provided by Firebase Remote Config. There is a crucial conversion function, `EmojiItem.toBatteryStyle()`, that bridges the remote data structure to the UI's domain model.
    *   **`CustomizationConfig`:** A model for persisting all user-selected customization settings. It has been updated to support direct image URLs, bypassing the need for a `selectedStyleId`.
*   **Repositories (Interfaces):** `CustomizationRepository` defines the contract for data access.
*   **Use Cases:** `LoadCustomizationUseCase` and `SaveCustomizationUseCase` encapsulate specific business logic.

### Data Layer
*   **Repository Implementations:**
    *   **`CustomizationRepositoryImpl`:** Implements persistence for user settings using Jetpack DataStore.
*   **Services:**
    *   **`EmojiCategoryService` & `EmojiItemService`:** These services are responsible for fetching data from Firebase Remote Config. `EmojiCategoryService` fetches a list of categories from the `emoji_categories` key, and `EmojiItemService` fetches items for each category from separate keys (e.g., `hot_category`, `animal_category`).

## 4. Data Flow & Content Management

The module uses a granular data-loading strategy from Firebase Remote Config (RC):

1.  The `BatteryGalleryViewModel` uses `EmojiCategoryService` to get the list of category tabs.
2.  When a user selects a category, it uses `EmojiItemService` to fetch the corresponding items for that category ID.
3.  These `EmojiItem` objects are then converted into `BatteryStyle` objects for the UI adapter.
4.  When a user navigates to the `EmojiCustomizeActivity`, the activity now loads all items from the selected category, allowing for mix-and-match customization.

The fallback mechanism is unified and relies on the standard Firebase Remote Config SDK functionality. The app uses `remote_config_defaults.xml` to provide default values for categories and items. If the network fetch fails, the SDK automatically returns the values from this XML file, ensuring a consistent and reliable user experience.
